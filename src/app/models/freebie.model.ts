import { CartItem } from './product.model';

/**
 * Freebie product interface
 */
export interface FreebieProduct {
  id: string;
  name: string;
  sku: string;
  child_sku?: string;
  product_id: string;
  variant_id?: string;
  amount: number; // Minimum cart amount required for this freebie
  thumbnail_image?: string;
  variant_name?: string;
  selling_price?: number;
  available_qty: number;
  start_date: number;
  end_date: number;
  store_id: string;
}

/**
 * Freebie cart item interface - extends CartItem with freebie-specific properties
 */
export interface FreebieCartItem extends CartItem {
  is_freebie: true;
  freebie_id: string;
  freebie_amount: number;
  freebie_name: string;
}
